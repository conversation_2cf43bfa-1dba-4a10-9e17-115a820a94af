{"The :attribute must contain at least one letter.": "The :attribute must contain at least one letter.", "The :attribute must contain at least one number.": "The :attribute must contain at least one number.", "The :attribute must contain at least one symbol.": "The :attribute must contain at least one symbol.", "The :attribute must contain at least one uppercase and one lowercase letter.": "The :attribute must contain at least one uppercase and one lowercase letter.", "The given :attribute has appeared in a data leak. Please choose a different :attribute.": "The given :attribute has appeared in a data leak. Please choose a different :attribute.", "You have successfully updated your personal information.": "Bạn đã cập nhật thông tin cá nhân thành công.", "You have successfully changed your password.": "Bạn đã thay đổi mật khẩu thành công.", "You have successfully saved data.": "Bạn đã lưu dữ liệu thành công.", "You have successfully deleted data.": "Bạn đã xóa dữ liệu thành công.", "The provided password does not match your current password.": "<PERSON><PERSON><PERSON> kh<PERSON>u hiện tại không ch<PERSON>h xác.", "throttle": "Bạn đã yêu cầu vư<PERSON>t quá số lần cho phép (:total lần).\nXin vui lòng thử lại sau :seconds giây.", "updateSurvey": "<PERSON><PERSON><PERSON> n<PERSON>t k<PERSON><PERSON> s<PERSON>t", "createNewSurvey": "<PERSON><PERSON><PERSON><PERSON> mới k<PERSON> s<PERSON>t", "checkbox": "Checkbox", "select": "<PERSON><PERSON>down", "text": "<PERSON><PERSON> luận", "text_with_star": "Tự luận có sao", "public.none": "None", "public.platform": "<PERSON><PERSON><PERSON> t<PERSON>", "public.app": "Ứng dụng", "public.platform_app": "<PERSON><PERSON> hai", "createNewAttachedSurvey": "<PERSON><PERSON><PERSON><PERSON> mới k<PERSON><PERSON>o s<PERSON>n", "updateAttachedSurvey": "<PERSON><PERSON><PERSON> n<PERSON><PERSON>t k<PERSON><PERSON><PERSON> s<PERSON> ch<PERSON>n", "Validation Exception": "エラーが発生しました", "feedType.general": "総合", "feedType.friend": "友だち", "feedType.follow": "フォロー", "feedType.latest": "新着のポスト", "feedType.recommended_answer": "評価の高い回答", "feedType.laugh": "オーギリ", "feedType.rate_none": "", "feedType.owner": "あなたのアクション", "feedType.topics": "TOPICS", "feedType.discover": "Discover", "feedType.raise_hand": "体験者レビュー", "postType.default": "Free", "postType.smile": "ゆるい質問・相談", "postType.not_smile": "真剣な質問・相談", "postType.laugh": "オーギリ", "postType.raise_hand": "体験者レビュー", "postType.multiple": "どっち", "postType.gender": "性別", "user.status.0": "削除", "user.status.1": "アクティブ", "user.status.2": "凍結", "user.status.3": "検証済み", "post.status.0": "削除", "post.status.1": "アクティブ", "postAnswer.status.0": "削除", "postAnswer.status.1": "アクティブ", "postAnswer.level.1": "アンサー", "postAnswer.level.2": "リアンサー", "postAnswer.level.3": "リリアンサー", "and": "と:usernameさん", "and.other": "と他:amount人", "rewarded": "ベストアンサー: :amountコイン\n", "notification.0": "「:post_content」という投稿に:usernameさん:andがAnswerrしました。", "notification.1": "「:answer_content」のAnswerrに:usernameさんから:amountコインがプレゼントされました。", "notification.2": "「:answer_content」のAnswerrに:usernameさんが:amountいいねしました。", "notification.3": "「:answer_content」のAnswerrに:usernameさん:andがリアンサーしました。", "notification.4": ":jobのお仕事の結果が承認されました:amountコインがプレゼントされます。", "notification.5": ":jobのお仕事の結果が承認されました:amountポイントがプレゼントされます。", "notification.6": ":jobのお仕事の結果は承認されませんでした。", "notification.7": "運営から:jobのお仕事が届きました。", "notification.8": ":usernameさんからフォローされました。", "notification.9": "「:post_content」の質問が:usernameさん:andから応援されました。", "notification.10": "「:post_content」の質問のBEST ANSWERRを決めましょう。", "notification.11": ":rewardedText「:answer_content」のAnswerに:usernameさんからBest Answerrが付与されました。", "notification.12": ":usernameさんからあなたに質問が届きました。", "notification.13": ":usernameさんが困っているみたいだよ。", "notification.14": "ようこそanswerrへ！招待リンクを使って登録してくれたので特別に:amount:unitをプレゼントするよ。", "notification.15": "おめでとう！あなたの紹介でユーザーが増えたのでコインをプレゼントするよ。", "notification.16": ":usernameから友だち申請が届きました。", "notification.17": ":usernameと友だちになりました。", "notification.18": ":usernameがコミュニティ「:community」に招待しました。", "notification.19": "あなたが紹介したユーザーがanswerrで大活躍！お礼に招待枠を1つプレゼントするよ。", "notification.20": "「:answer_content」のAnswerrに:usernameさん:andがいいねしました。", "notification.21": "プロフィール入力ありがとう！\n1コインプレゼントするよ。", "notification.22": "おめでとう！投稿に初めてのアンサーが付きました。\n1コインプレゼントするよ。", "notification.23": "職業情報の更新ありがとう！\n:amountコインプレゼントするよ。", "notification.24": "性別の更新ありがとう！\n:amountコインプレゼントするよ。", "Invalid post answer ID.": "Invalid post answer ID.", "Invalid post ID.": "Invalid post ID.", "You have not donated this post answer yet.": "You have not donated this post answer yet.", "You can not donate your post answer.": "You can not donate your post answer.", "You can not like your answer.": "You can not like your answer.", "You can not like your post.": "You can not like your post.", "You have liked this post.": "You have already liked this post.", "Invalid user ID.": "Invalid user ID.", "You have not liked this answer yet.": "You have not liked this answer yet.", "You have not liked this post yet.": "You have not liked this post yet.", "You do not have permission to perform this action.": "You do not have permission to perform this action.", "You have already voted best-answer for this post.": "You have already voted best-answer for this post.", "Invalid question ID.": "Invalid question ID.", "Invalid notification ID.": "Invalid notification ID.", "Your account has been deleted or disabled.": "Your account has been deleted or disabled.", "Invalid attached ID.": "Invalid attached ID.", "Invalid survey ID.": "Invalid survey ID.", "Invalid attached survey ID.": "Invalid attached survey ID.", "You do not have enough points to perform this action.": "押した回数が所有ポイントの上限を超えています。", "You do not have enough coins to perform this action.": "押した回数が所有コインの上限を超えています。", "You cannot unlike this answer because it has expired.": "You cannot unlike this answer because it has expired.", "You cannot cancel this donation because it has expired.": "You cannot cancel this donation because it has expired.", "profileType.student": "<PERSON><PERSON><PERSON> sinh / <PERSON>h viên", "profileType.employee": "<PERSON><PERSON><PERSON><PERSON> đi làm", "profileType.other": "K<PERSON><PERSON><PERSON>", "profileType.": "N/A", "- Field of work: ": "- Field of work: ", "- Field of expertise: ": "- Field of expertise: ", "- Service in charge: ": "- Service in charge: ", "Instruct: Given a web search query, retrieve relevant passages that answer the query.": "Instruct: Given a web search query, retrieve relevant passages that answer the query.", "Instruct: Given a web search query, retrieve relevant passages that are similar to the query.": "Instruct: Given a web search query, retrieve relevant passages that are similar to the query.", "otp_choice": "{1} You can only resend O<PERSON> after :second second.|[2,*] You can only resend O<PERSON> after :second seconds.", "An error occurred while uploading the image.": "An error occurred while uploading the image.", "An error occurred while uploading the avatar.": "An error occurred while uploading the avatar.", "validationException.": "Validation Exception", "validationException.updateInformation": "すべての情報をご入力いただき、プロフィールを完成させてください。", "news.status.0": "削除", "news.status.1": "アクティブ", "Invalid news ID.": "Invalid news ID.", "Age must be greater than 12 years old.": "Tuổi phải lớn hơn 12 tuổi.", "Your phone number has been already existed.": "<PERSON><PERSON> điện thoại đã tồn tại trên hệ thống.", "Your account does not exist.": "<PERSON><PERSON><PERSON> khoản của bạn không tồn tại.", "Your referrer code is invalid.": "<PERSON><PERSON> giới thiệu của bạn không hợp lệ.", "quest.status.0": "削除", "quest.status.1": "アクティブ", "quest.type.answered": "Đã trả lời câu hỏi", "quest.type.followed": "<PERSON><PERSON> đ<PERSON><PERSON> ng<PERSON><PERSON> khác theo dõi", "quest.type.friend": "<PERSON><PERSON> thêm bạn", "quest.type.invited": "<PERSON><PERSON> mời người khác tham gia", "quest.type.voted": "<PERSON><PERSON><PERSON> trả lời đã đ<PERSON><PERSON><PERSON> bình ch<PERSON>n", "quest.type.hobby": "Sở thích", "quest.type.career": "<PERSON>h<PERSON>ng tin nghề nghiệp", "quest.type.gender": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h", "quest.unit.point": "ポイント", "quest.unit.coin": "コイン", "existedQuestForType": "Đã tồn tại quest cho loại: :type.", "quest.answered.amount": "+:amount :unit trở lên", "quest.followed.amount": "+:amount :unit", "quest.friend.amount": "+:amount:unit", "quest.invited.amount": "+:amount :unit", "quest.voted.amount": "+:amount :unit trở lên", "quest.hobby.amount": "+:amount :unit", "quest.career.amount": "+:amount :unit", "quest.gender.amount": "+:amount :unit", "You have already blocked this user.": "Bạn đã chặn người dùng này rồi.", "blockedMessage": "情報を見ることができません。", "You do not have permission to perform this action in this community.": "この投稿はコミュニティに属しています。\n回答や投稿をするには、コミュニティに参加する必要があります。", "You already have this premium feature activated.": "Bạn đã kích hoạt t<PERSON>h năng này r<PERSON>.", "You can not like more than 5 points.": "You can not like more than 5 points.", "gender.0": "", "gender.1": "男性", "gender.2": "女性", "gender.3": "未回答", "premiumFeature.gender": "Mở khóa bài viết giới tính", "premiumFeature.pin_answer": "<PERSON>n c<PERSON>u trả lời", "Service Unavailable": "<PERSON><PERSON><PERSON> vụ tạm thời gián đoạn do bảo trì. <PERSON>g bạn thông cảm và thử lại sau.", "You do not have permission to pin this answer.": "You do not have permission to pin this answer.", "": ""}