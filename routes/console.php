<?php

use App\Http\Controllers\Web\AttachedSurveyController;
use App\Http\Controllers\Web\CommonController;
use App\Http\Controllers\Web\NewsController;
use App\Http\Controllers\Web\PostAnswerController;
use App\Http\Controllers\Web\PostController;
use App\Http\Controllers\Web\PremiumFeatureController;
use App\Http\Controllers\Web\QuestController;
use App\Http\Controllers\Web\SurveyController;
use App\Http\Controllers\Web\UserController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use Laravel\Jetstream\Http\Controllers\Inertia\UserProfileController;

/**
 * console routes
 */
Route::domain(config('app.console_domain'))
    ->middleware([
        'auth:sanctum',
        config('jetstream.auth_session'),
        'verified',
        'admin',
    ])
    ->group(function () {
        /**
         * post
         */
        Route::get('/post', [PostController::class, 'index'])->name('post.list');
        Route::post('/post/delete', [PostController::class, 'delete'])->name('post.delete');
        Route::get('/post/view-history', [PostController::class, 'history'])->name('postViewHistory');
        Route::get('/post/{post}', [PostController::class, 'detail'])
            ->name('post.detail')
            ->whereNumber('post');
        Route::post('/post/toggle-feature', [PostController::class, 'toggleFeature'])->name('post.toggleFeature');

        /**
         * post answer
         */
        Route::get('/post-answer', [PostAnswerController::class, 'index'])->name('postAnswer.list');
        Route::post('/post-answer/delete', [PostAnswerController::class, 'delete'])->name('postAnswer.delete');
        Route::get('/post-answer/{postAnswer}', [PostAnswerController::class, 'detail'])
            ->name('postAnswer.detail')
            ->whereNumber('postAnswer');

        /**
         * survey
         */
        Route::get('/survey', [SurveyController::class, 'listSurvey'])->name('survey.list');
        Route::get('/survey/create', [SurveyController::class, 'form'])->name('survey.create');
        Route::get('/survey/update/{survey}', [SurveyController::class, 'form'])
            ->name('survey.update')
            ->whereNumber('survey');

        Route::post('/survey/store', [SurveyController::class, 'store'])->name('survey.store');
        Route::get('/survey/sort', [SurveyController::class, 'surveySortView'])->name('surveySort');
        Route::post('/survey/sort', [SurveyController::class, 'surveySortStore']);
        Route::post('/survey/pinia-store', [SurveyController::class, 'piniaStoreData']);
        Route::post('/survey/list-questions', [SurveyController::class, 'listQuestion'])->name('survey.listQuestion');
        Route::post('/survey/delete', [SurveyController::class, 'delete'])->name('survey.delete');

        /**
         * attached survey
         */
        Route::get('/attached-survey', [AttachedSurveyController::class, 'index'])->name('attachedSurvey.list');
        Route::get('/attached-survey/create', [AttachedSurveyController::class, 'form'])->name('attachedSurvey.create');
        Route::get('/attached-survey/update/{attachedSurvey}', [AttachedSurveyController::class, 'form'])
            ->name('attachedSurvey.update')
            ->whereNumber('attachedSurvey');

        Route::post('/attached-survey/delete', [AttachedSurveyController::class, 'delete'])->name('attachedSurvey.delete');
        Route::post('/attached-survey/store', [AttachedSurveyController::class, 'store'])->name('attachedSurvey.store');

        /**
         * user
         */
        Route::get('/', [UserController::class, 'index'])->name('user.list');
        Route::get('/user/attribute/{user}', [UserController::class, 'attribute'])
            ->name('user.attribute')
            ->whereNumber('user');

        Route::get('/user/feed/{user}', [UserController::class, 'feed'])
            ->name('user.feed')
            ->whereNumber('user');

        Route::post('/user/delete', [UserController::class, 'delete'])->name('user.delete');
        Route::get('/user/{user}', [UserController::class, 'detail'])
            ->name('user.detail')
            ->whereNumber('user');

        /**
         * profile
         */
        Route::get('/profile', [UserProfileController::class, 'show'])
            ->name('profile.detail');

        Route::get('/profile/change-password', function () {
            return Inertia::render('Profile/ChangePassword');
        })->name('profile.changePassword');

        /**
         * common
         */
        Route::get('/settings', [CommonController::class, 'settings'])->name('system.settings');
        Route::post('/settings/store-settings', [CommonController::class, 'storeSettings'])->name('setting.store');

        /**
         * assistant
         */
        #Route::get('/assistant', [AssistantController::class, 'index'])->name('assistant');
        #Route::post('/assistant/store', [AssistantController::class, 'store'])->name('assistant.store');

        /**
         * news
         */
        Route::get('/news', [NewsController::class, 'index'])->name('news.list');
        Route::get('/news/form/{news?}', [NewsController::class, 'form'])->name('news.form');
        Route::post('/news/store', [NewsController::class, 'store'])->name('news.store');
        Route::post('/news/delete', [NewsController::class, 'destroy'])->name('news.delete');

        /**
         * quest
         */
        Route::get('/quest', [QuestController::class, 'index'])->name('quest.list');
        Route::get('/quest/form/{quest?}', [QuestController::class, 'form'])->name('quest.form');
        Route::post('/quest/store', [QuestController::class, 'store'])->name('quest.store');
        Route::post('/quest/toggle', [QuestController::class, 'toggle'])->name('quest.toggle');

        /**
         * premium features
         */
        Route::get('/premium-feature', [PremiumFeatureController::class, 'index'])->name('premiumFeature.list');
        Route::get('/premium-feature/form/{feature?}', [PremiumFeatureController::class, 'form'])->name('premiumFeature.form');
        Route::post('/premium-feature/store', [PremiumFeatureController::class, 'store'])->name('premiumFeature.store');
    }
);
