<script setup>
import { computed, inject, reactive } from "vue";
import { useI18n } from "vue-i18n";
import { clearEmptyData, formatDate } from "@/Utils/index.js";
import { Link as InertiaLink, router, useForm, usePage } from '@inertiajs/vue3';

import DataTable from 'primevue/datatable';
import Column from 'primevue/column';

import AppLayout from '@/Layouts/AppLayout.vue';
import InputLabel from "@/Components/InputLabel.vue";
import SearchInput from "@/Components/SearchInput.vue";
import Pagination from "@/Components/Pagination.vue";
import LoadingIcon from "@/Components/LoadingIcon.vue";
import SecondaryButton from "@/Components/SecondaryButton.vue";
import RedButton from "@/Components/RedButton.vue";
import Modal from "@/Components/Modal.vue";
import FixedSelectionBox from "@/Components/FixedSelectionBox.vue";
import { route } from "ziggy-js";
import GridContainer from "@/Components/GridContainer.vue";

const $toast = inject('$toast');
const page = usePage();
const { t } = useI18n();

const props = defineProps({
    filters: Object,
    posts: Object,
});

const form = useForm({
    search: props.filters['search'] ?? '',
    user: props.filters['user'] ?? '',
    reported: props.filters['reported'] ?? '',
    topics: props.filters['topics'] ?? '',
    limit: props.filters['limit'] ?? 10,
});

const config = reactive({
    showSearchClearButton: (props.filters.search ?? '').trim().length > 0,
    showUserSearchClearButton: (props.filters.user ?? '').trim().length > 0,
    searching: false,
    showModal: false,
    open: false,
    deleteId: null,
    deleting: false,
    selectOptions: [
        {
            value: 'yes',
            label: t('yes'),
        },
        {
            value: 'no',
            label: t('no'),
        },
    ],
    showFeatureModal: false,
    openFeature: false,
    featureId: null,
    processing: false,
    currentFeatureState: 0,
    activePage: null,
    busy: computed(() => config.activePage !== null || config.searching || form.processing),
});

const _search = () => {
    if (form.processing) {
        return false;
    }

    form.transform(data => clearEmptyData(data)).get(route('post.list'), {
        preserveScroll: true,
        onSuccess: () => {},
    });
}

const search = () => {
    config.showSearchClearButton = true;
    _search();
}

const searchUser = () => {
    config.showUserSearchClearButton = true;
    _search();
}

const clearSearch = () => {
    form.search = '';
    config.showSearchClearButton = false;
    _search();
}

const clearUserSearch = () => {
    form.user = '';
    config.showUserSearchClearButton = false;
    _search();
}

const confirmDeletePost = (id) => {
    config.deleteId = id;
    config.showModal = true;
    setTimeout(() => config.open = true, 150);
}

const closeModal = async () => {
    config.open = false;
    setTimeout(() => config.showModal = false, 150);
}

const closeFeatureModal = async () => {
    config.openFeature = false;
    setTimeout(() => config.showFeatureModal = false, 150);
}

const deletePost = () => {
    if (config.deleting) {
        return;
    }

    router.post(route('post.delete'), {
        id: config.deleteId,
    }, {
        preserveScroll: true,
        preserveState: true,
        onBefore: () => {
            config.deleting = true;
        },
        onSuccess: () => {
            config.deleteId = null;

            if (page.props.jetstream.flash.message) {
                $toast.success(page.props.jetstream.flash.message);
            }

            closeModal();
        },
        onFinish: () => {
            config.deleting = false;
        },
    });
}

const showFeatureModal = (id, currentState) => {
    config.featureId = id;
    config.showFeatureModal = true;
    config.currentFeatureState = currentState;
    setTimeout(() => config.openFeature = true, 150);
}

const toggleFeature = () => {
    if (config.processing) {
        return;
    }

    router.post(route('post.toggleFeature'), {
        id: config.featureId,
    }, {
        preserveScroll: true,
        preserveState: true,
        onBefore: () => {
            config.processing = true;
        },
        onSuccess: () => {
            config.featureId = null;

            if (page.props.jetstream.flash.message) {
                $toast.success(page.props.jetstream.flash.message);
            }

            closeFeatureModal();
        },
        onFinish: () => {
            config.processing = false;
        },
    });
}

const updateProgress = (progress) => {
    config.activePage = progress;
    config.searching = true;
};

const changeLimit = (limit) => {
    form.limit = limit;
    form.search = '';
    form.user = '';
    form.reported = '';
    form.topics = '';

    _search();
}
</script>

<template>
    <app-layout :title="$t('listPost')">
        <template v-slot:header>
            <h2
                class="text-xl text-gray-800 leading-tight mr-auto"
                v-text="$t('listPost')"
            />
        </template>

        <div class="p-6 sm:mx-2">
            <div class="flex items-stretch">
                <div class="mt-0 w-80 mr-8">
                    <input-label for="search" :value="$t('search')" />

                    <search-input
                        id="search"
                        class="mt-1 block w-full"
                        v-model="form.search"
                        :placeholder="$t('postSearch')"
                        :disabled="config.busy"
                        :show-clear-button="config.showSearchClearButton"
                        @input="config.showSearchClearButton = false"
                        @clear-search="clearSearch"
                        @enter="search"
                    />
                </div>

                <div class="mt-0 w-96 mr-8">
                    <input-label for="search-user" :value="$t('createdBy')" />

                    <search-input
                        id="search-user"
                        class="mt-1 block w-full"
                        v-model="form.user"
                        :placeholder="$t('postUserSearch')"
                        :disabled="config.busy"
                        :show-clear-button="config.showUserSearchClearButton"
                        @input="config.showUserSearchClearButton = false"
                        @clear-search="clearUserSearch"
                        @enter="searchUser"
                    />
                </div>

                <div class="mt-0 w-64 mr-8">
                    <input-label for="report-search" :value="$t('reportStatus')" class="mb-1" />

                    <fixed-selection-box
                        v-model="form.reported"
                        :placeholder="$t('all')"
                        :disabled="config.busy"
                        :options="config.selectOptions"
                        :clearable="true"
                        @cleared="_search"
                        @selected="_search"
                    />
                </div>

                <div class="mt-0 w-64 mr-8">
                    <input-label for="topics-search" :value="$t('TOPICS')" class="mb-1" />

                    <fixed-selection-box
                        v-model="form.topics"
                        :placeholder="$t('all')"
                        :disabled="config.busy"
                        :options="config.selectOptions"
                        :clearable="true"
                        @cleared="_search"
                        @selected="_search"
                    />
                </div>
            </div>

            <grid-container :loading="config.busy">
                <data-table :value="posts['data']">
                    <column class="number-column" :header="$t('ID')">
                        <template #body="{ data }">
                            <inertia-link
                                class="hover:text-sky-600 hover:underline"
                                v-text="data['post_id']"
                                :href="route('post.detail', { post: data['post_id'] })"
                            />
                        </template>
                    </column>

                    <column class="post-username-column" :header="$t('createdBy')">
                        <template #body="{ data }">
                            <inertia-link
                                class="hover:text-sky-600 hover:underline"
                                v-text="data['username']"
                                :href="route('post.list', { user: data['user_id'] })"
                            />
                        </template>
                    </column>

                    <column class="number-column extra" :header="$t('createdByID')">
                        <template #body="{ data }">
                            <inertia-link
                                class="hover:text-sky-600 hover:underline"
                                v-text="data['user_id']"
                                :href="route('post.list', { user: data['user_id'] })"
                            />
                        </template>
                    </column>

                    <column class="title-flex-column" :header="$t('postContent')">
                        <template #body="{ data }">
                            <div v-html="data['content']" />
                        </template>
                    </column>

                    <column class="post-type-column" field="tag" :header="$t('postType')" />

                    <column class="time-column" :header="$t('postCreatedAt')">
                        <template #body="{ data }">
                            {{ formatDate(data['created_at']) }}
                        </template>
                    </column>

                    <column class="count-column" :header="$t('answerCount')">
                        <template #body="{ data }">
                            <inertia-link
                                v-if="data['answer_count'] > 0"
                                class="hover:text-red-600 hover:underline"
                                v-text="data['answer_count']"
                                :href="route('postAnswer.list', { post: data['post_id'] })"
                            />

                            <template v-else>
                                {{ data['answer_count'] }}
                            </template>
                        </template>
                    </column>

                    <column class="count-column" field="report_count" :header="$t('reportCount')" />
                    <column class="status-column" field="status_label" :header="$t('status')" />
                    <column class="action-column">
                        <template #body="{ data }">
                            <inertia-link
                                :href="route('post.detail', { post: data['post_id'] })"
                            >
                                <i class="pi pi-info-circle text-gray-500 hover:text-sky-600" />
                            </inertia-link>

                            <i
                                v-if="parseInt(data['status']) !== 0"
                                class="pi pi-trash text-red-400 hover:text-red-600 hover:cursor-pointer"
                                @click="confirmDeletePost(data['post_id'])"
                            />

                            <i
                                v-if="data['status'] === 1"
                                class="pi pi-flag hover:text-lime-700 hover:cursor-pointer"
                                :class="[data['featured'] === 1 ? 'text-lime-700' : 'text-gray-400']"
                                @click="showFeatureModal(data['post_id'], data['featured'])"
                            />
                        </template>
                    </column>

                    <template #empty>{{ $t((filters.search && filters.search !== '') || (filters.user && filters.user !== '') ? 'emptyResult' : 'emptyData') }}</template>
                </data-table>
            </grid-container>

            <pagination
                v-if="posts['data'].length > 0"
                class="mt-5 flex items-center justify-center mx-auto"
                :links="posts['links']"
                :active="config.activePage"
                :disabled="config.busy"
                :limit="posts['per_page']"
                :total="posts['total']"
                :from="posts['from']"
                :to="posts['to']"
                @progress="updateProgress"
                @changeLimit="changeLimit"
            />
        </div>
    </app-layout>

    <modal v-if="config.showModal" :show="config.open" @close="closeModal">
        <div class="pt-4 pb-3 px-3 font-semibold" v-text="$t('confirm')" />
        <div class="border-t border-b p-4" v-text="$t('confirmDeletePostMessage')" />
        <div class="flex items-center justify-end px-3 py-3">
            <secondary-button class="mr-3 text-sm" v-text="$t('cancel')" @click="closeModal" />

            <red-button class="text-sm overflow-hidden h-[34px]" @click="deletePost">
                <loading-icon v-if="config.deleting" class="mr-1" />

                <span class="text-sm text-white" v-text="$t('delete')" />
            </red-button>
        </div>
    </modal>

    <modal v-if="config.showFeatureModal" :show="config.openFeature" @close="closeFeatureModal">
        <div class="pt-4 pb-3 px-3 font-semibold" v-text="$t('confirm')" />
        <div class="border-t border-b p-4" v-text="$t('confirm' + (config.currentFeatureState === 0 ? 'EnableFeature' : 'DisableFeature') + 'Message')" />
        <div class="flex items-center justify-end px-3 py-3">
            <secondary-button class="mr-3 text-sm" v-text="$t('cancel')" @click="closeFeatureModal" />

            <red-button class="text-sm overflow-hidden h-[34px]" @click="toggleFeature">
                <loading-icon v-if="config.processing" class="mr-1" />

                <span class="text-sm text-white" v-text="$t('confirm')" />
            </red-button>
        </div>
    </modal>
</template>
