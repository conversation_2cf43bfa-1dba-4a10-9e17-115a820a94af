import { defineStore, acceptHMRUpdate } from 'pinia';

export const useSurveyStore = defineStore({
    id: 'data-stores',
    state: () => ({
        surveys: [],
    }),

    getters: {
        hasData: state => state.surveys.length > 0,
        data: state => state.surveys,
    },

    actions: {
        setData (dictionaries) {
            this.surveys = dictionaries;
        },

        clear() {
            this.surveys = [];
        },

        async loadData() {
            await window.axios.post('/survey/pinia-store')
                .then(response => {
                    this.setData(response.data);
                })
                .catch((e) => {
                    console.log(e);
                });
        },
    },
});

if (import.meta.hot) {
    import.meta.hot.accept(acceptHMRUpdate(useSurveyStore, import.meta.hot));
}
