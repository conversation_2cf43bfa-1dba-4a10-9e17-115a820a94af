<?php

namespace App\Enums;

enum PostType: string
{
    case DEFAULT = 'default';

    case SMILE = 'smile';

    case NOT_SMILE = 'not_smile';

    case LAUGH = 'laugh';

    case RAISE_HAND = 'raise_hand';

    case WITH_TAG = 'with_tag';

    case MULTIPLE = 'multiple';

    case GENDER = 'gender';

    /**
     * @return array
     */
    public static function toArray(): array
    {
        return [
            self::DEFAULT->value,
            self::SMILE->value,
            self::NOT_SMILE->value,
            self::LAUGH->value,
            self::RAISE_HAND->value,
            self::WITH_TAG->value,
            self::MULTIPLE->value,
            self::GENDER->value,
        ];
    }

    /**
     * @return string
     */
    public function toLabel()
    {
        return __('postType.' . $this->value);
    }
}
