<?php

namespace App\Providers;

use App\Helpers\ApiResponse;
use App\Helpers\ChatWork;
use App\Helpers\Encryptor;
use App\Helpers\LazyResponseData;
use App\Helpers\FirebaseCloudMessage;
use App\Helpers\OneTimePassword;
use App\Helpers\QueryLog;
use App\Helpers\QuestHelper;
use App\Helpers\Storage;
use App\Helpers\UserBlockHelper;
use App\Models\Comment;
use App\Models\Community;
use App\Models\Post;
use App\Models\PostAnswer;
use App\Models\User;
use App\Repositories\AnswerRepository;
use App\Repositories\AttachedSurveyRepository;
use App\Repositories\CommentRepository;
use App\Repositories\CommunityRepository;
use App\Repositories\CommunityUserRepository;
use App\Repositories\FeedViewedStatRepository;
use App\Repositories\NewsRepository;
use App\Repositories\PostAnswerRepository;
use App\Repositories\PostRepository;
use App\Repositories\PremiumFeatureRepository;
use App\Repositories\QuestionRepository;
use App\Repositories\QuestRepository;
use App\Repositories\SettingRepository;
use App\Repositories\SurveyRepository;
use App\Repositories\UserRepository;
use App\Repositories\PostViewHistoryRepository;
use App\Repositories\ViewedPostRepository;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Events\QueryExecuted;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->registerSingletons();
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Schema::defaultStringLength(191);

        /**
         * force https scheme
         */
        if (config('app.useHttps')) {
            URL::forceScheme('https');
        }

        Relation::enforceMorphMap([
            'post' => Post::class,
            'post_answer' => PostAnswer::class,
            'comment' => Comment::class,
            'user' => User::class,
            'community' => Community::class,
        ]);

        /**
         * query log debug
         */
        if (config('database.logQuery')) {
            DB::listen(function (QueryExecuted $query) {
                Log::channel('dbQuery')->debug('SQL: ' . $query->sql, [
                    ...$query->bindings,
                    ...[
                        'time' => $query->time,
                    ],
                ]);
            });
        }
    }

    /**
     * @return void
     */
    protected function registerSingletons(): void
    {
        $singletons = [
            /**
             * repositories
             */
            AnswerRepository::class,
            AttachedSurveyRepository::class,
            CommentRepository::class,
            CommunityRepository::class,
            CommunityUserRepository::class,
            FeedViewedStatRepository::class,
            NewsRepository::class,
            PostRepository::class,
            PostAnswerRepository::class,
            PremiumFeatureRepository::class,
            QuestionRepository::class,
            QuestRepository::class,
            SettingRepository::class,
            SurveyRepository::class,
            UserRepository::class,
            PostViewHistoryRepository::class,
            ViewedPostRepository::class,

            /**
             * helper services
             */
            ApiResponse::class,
            ChatWork::class,
            Encryptor::class,
            LazyResponseData::class,
            FirebaseCloudMessage::class,
            OneTimePassword::class,
            QueryLog::class,
            QuestHelper::class,
            Storage::class,
        ];

        foreach ($singletons as $singleton) {
            $this->app->singleton($singleton, function () use ($singleton) {
                return new $singleton();
            });
        }

        $this->app->singleton(UserBlockHelper::class, function () {
            return new UserBlockHelper(app(UserRepository::class));
        });
    }
}
