<?php

namespace App\Http\Controllers\Api\V1;

use App\Actions\Post\FetchUserListPost;
use App\Actions\User\AddDeviceToken;
use App\Actions\User\AddFriend;
use App\Actions\User\BlockUser;
use App\Actions\User\CancelFriendInvitation;
use App\Actions\User\CreateNewInviteToken;
use App\Actions\User\CreateNewUser;
use App\Actions\User\DeleteUser;
use App\Actions\User\FetchRecommendation;
use App\Actions\User\FetchUserRelation;
use App\Actions\User\FetchUserNotification;
use App\Actions\User\FetchUserProfile;
use App\Actions\User\FindFriend;
use App\Actions\User\FindSchoolmate;
use App\Actions\User\IgnoreFriendInvitation;
use App\Actions\User\InviteFriend;
use App\Actions\User\ListBlockedUser;
use App\Actions\User\ListPendingFriend;
use App\Actions\User\LogoutUser;
use App\Actions\User\MarkNotificationWasRead;
use App\Actions\User\ResetBadgeCount;
use App\Actions\User\UnblockUser;
use App\Actions\User\Unfriend;
use App\Actions\User\UpdateCareer;
use App\Actions\User\UpdateHobby;
use App\Actions\User\UpdateInformation;
use App\Actions\User\UpdatePhoneNumber;
use App\Enums\Gender;
use App\Enums\ProfileType;
use App\Events\GenderUpdated;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Traits\HasPagination;
use App\Http\Controllers\Traits\HasPostOrPostAnswerResponse;
use App\Http\Requests\Api\Post\ListUserPostRequest;
use App\Http\Requests\Api\User\AddDeviceTokenRequest;
use App\Http\Requests\Api\User\AddFriendRequest;
use App\Http\Requests\Api\User\BlockUserRequest;
use App\Http\Requests\Api\User\CancelFriendInvitationRequest;
use App\Http\Requests\Api\User\CheckPhoneExistRequest;
use App\Http\Requests\Api\User\FirebaseAuthenticateRequest;
use App\Http\Requests\Api\User\FetchProfileRequest;
use App\Http\Requests\Api\User\FindFriendRequest;
use App\Http\Requests\Api\User\GetRelationRequest;
use App\Http\Requests\Api\User\GetNotificationRequest;
use App\Http\Requests\Api\User\IgnoreFriendInvitationRequest;
use App\Http\Requests\Api\User\ImportRequest;
use App\Http\Requests\Api\User\InviteFriendRequest;
use App\Http\Requests\Api\User\MarkNotificationWasReadRequest;
use App\Http\Requests\Api\User\RecommendationRequest;
use App\Http\Requests\Api\User\UnblockUserRequest;
use App\Http\Requests\Api\User\UnfriendRequest;
use App\Http\Requests\Api\User\UpdateCareerRequest;
use App\Http\Requests\Api\User\UpdateGenderRequest;
use App\Http\Requests\Api\User\UpdateInformationRequest;
use App\Http\Requests\Api\User\UpdateHobbyRequest;
use App\Http\Requests\Api\User\UpdatePhoneNumberRequest;
use App\Http\Requests\Api\User\VerifyInvitedLinkRequest;
use App\Http\Resources\Api\NotificationResource;
use App\Http\Resources\Api\PostResource;
use App\Http\Resources\Api\UserResource;
use App\Imports\UsersImport;
use App\Models\Community;
use App\Models\Notification;
use App\Models\User;
use Closure;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class UserController extends Controller
{
    use HasPagination;
    use HasPostOrPostAnswerResponse;

    /**
     * @param CheckPhoneExistRequest $request
     * @return array
     */
    public function checkPhoneExists(CheckPhoneExistRequest $request)
    {
        return apiResponse()->success([
            'exists' => (int) $request->phoneExisted(),
        ]);
    }

    /**
     * @param VerifyInvitedLinkRequest $request
     * @param CreateNewInviteToken $processor
     * @return array
     */
    public function verifyInvitedLink(VerifyInvitedLinkRequest $request, CreateNewInviteToken $processor)
    {
        $inviteToken = $processor->excute($request->getReferrerUser());

        return apiResponse()->success([
            'inviteToken' => $inviteToken->token,
        ]);
    }

    /**
     * @param UpdateInformationRequest $request
     * @param UpdateInformation $processor
     * @return array
     * @throws ValidationException
     */
    public function updateInformation(UpdateInformationRequest $request, UpdateInformation $processor): array
    {
        $user = $processor->execute($request);

        $user->loadCount([
            'friends' => function (Builder $query) {
                $query->whereHas('target', fn ($q) => $q->where('status', 1));
            },
        ]);

        if (config('app.debug')) {
            chatWork()->report([
                'url' => $request->fullUrl(),
                'data' => [
                    'post' => $request->post(),
                ],
            ]);
        }

        return apiResponse()->success([
            'user' => UserResource::make($user)->showProfile()->toArray($request),
        ]);
    }

    /**
     * @param UpdateCareerRequest $request
     * @param UpdateCareer $processor
     * @return array
     */
    public function updateCareer(UpdateCareerRequest $request, UpdateCareer $processor)
    {
        [$user, $amount, $unit] = $processor->execute($request);

        if (config('app.debug')) {
            chatWork()->report([
                'url' => $request->fullUrl(),
                'data' => [
                    'post' => $request->post(),
                ],
            ]);
        }

        return apiResponse()->success([
            'user' => UserResource::make($user)->showProfile()->toArray($request),
            'amount' => $amount,
            'unit' => $unit,
        ]);
    }

    /**
     * @param UpdateHobbyRequest $request
     * @param UpdateHobby $processor
     * @return array
     */
    public function updateHobby(UpdateHobbyRequest $request, UpdateHobby $processor)
    {
        $user = $request->user();
        [$amount, $unit] = $processor->execute($user, $request->post('brief'));

        return apiResponse()->success([
            'user' => UserResource::make($user)->showProfile()->toArray($request),
            'amount' => $amount,
            'unit' => $unit,
        ]);
    }

    /**
     * @param FetchProfileRequest $request
     * @param FetchUserProfile $fetcher
     * @return array
     */
    public function getProfile(FetchProfileRequest $request, FetchUserProfile $fetcher)
    {
        return apiResponse()->success(
            $fetcher->execute($request->getCurrentUser(), $request),
        );
    }

    /**
     * @param GetRelationRequest $request
     * @param FetchUserRelation $fetcher
     * @param string $relation
     * @param string|null $type
     * @return array
     * @throws BindingResolutionException
     */
    protected function getRelationData(GetRelationRequest $request, FetchUserRelation $fetcher, string $relation, ?string $type = null)
    {
        $extraSelect = match ($relation) {
            'friend' => 't.friend_at',
            'follow' => 't.followed_at',
            default => 't.blocked_at',
        };

        $payload = [
            'userId' => $request->getCurrentUser()->getKey(),
            'relation' => $relation,
            'relationType' => $type,
            'fetchTotal' => false,
            'extraSelect' => $extraSelect,
            'extraOrders' => [
                $extraSelect => 'desc',
                'users.user_id' => 'desc',
            ],
            'status' => 1,
            'inCommunityId' => $request->get('community_id'),
        ];

        list ($page, $limit) = $this->parsePagination($request);
        $offset = ($page - 1) * $limit;

        $users = $fetcher->execute($payload, $limit + 1, $page, $offset)->getCollection();
        if ($hasNextPage = $users->count() > $limit) {
            $users->pop();
        }

        return apiResponse()->success([
            'users' => UserResource::collection($users)->toArray($request),
            'hasNextPage' => (int) $hasNextPage,
        ]);
    }

    /**
     * @param ListUserPostRequest $request
     * @param FetchUserListPost $fetcher
     * @return array
     * @throws BindingResolutionException
     */
    public function listPosts(ListUserPostRequest $request, FetchUserListPost $fetcher)
    {
        $user = $request->getCurrentUser();

        [$page, $limit] = $this->parsePagination($request);
        $offset = ($page - 1) * $limit;

        $payload = [
            'userId' => $user->getKey(),
            'status' => 1,
            'fetchTotal' => false,
            'orderBy' => 'post_id',
            'orderDirection' => 'desc',
        ];

        /** @var Collection $posts */
        $posts = $fetcher->execute($payload, $limit + 1, $page, $offset)->getCollection();
        if ($hasNextPage = $posts->count() > $limit) {
            $posts->pop();
        }

        /**
         * load missing relationships
         */
        $this->loadPostMissing($posts, $request->user()->getKey());

        return apiResponse()->success([
            'posts' => $posts->transform(function($post) use ($request) {
                return PostResource::make($post)->toArray($request);
            }),
            'hasNextPage' => (int) $hasNextPage,
        ]);
    }

    /**
     * @param AddDeviceTokenRequest $request
     * @param AddDeviceToken $processor
     * @return array
     */
    public function addDeviceToken(AddDeviceTokenRequest $request, AddDeviceToken $processor)
    {
        $processor->execute($request->user(), $request->post('device_token'));

        return apiResponse()->success();
    }

    /**
     * @param GetNotificationRequest $request
     * @param FetchUserNotification $processor
     * @return array
     */
    public function getNotifications(GetNotificationRequest $request, FetchUserNotification $processor)
    {
        [$page, $limit] = $this->parsePagination($request, 100);
        $offset = ($page - 1) * $limit;

        $hasNextPage = false;
        $hasNextPagePin = false;
        $notifications = [];
        $pins = [];

        $user = $request->user();

        $type = (string) $request->get('type');
        if (in_array($type, ['all', 'notification'])) {
            $notifications = $processor->execute($user, $limit, $offset);

            /** @var Collection|Notification[] $notifications */
            if ($hasNextPage = $notifications->count() > $limit) {
                $notifications->pop();
            }

            /**
             * load post object
             */
            $answers = [];
            foreach ($notifications as $notification) {
                if ($notification->object_type === 'post_answer') {
                    $answers[] = $notification->object;
                }
            }

            $answers = Collection::make($answers);
            $answers->load(['post', 'parent']);
        }

        if (in_array($type, ['all', 'pin'])) {
            $pins = $processor->getPinNotifications($user, $limit, $offset);

            /** @var Collection|Notification[] $notifications */
            if ($hasNextPagePin = $pins->count() > $limit) {
                $pins->pop();
            }
        }

        return apiResponse()->success([
            'hasNextPage' => (int) $hasNextPage,
            'notifications' => ! empty($notifications) ? $notifications->transform($this->transform($request)) : [],
            'hasNextPagePin' => (int) $hasNextPagePin,
            'pins' => ! empty($pins) ? $pins->transform($this->transform($request)) : [],
        ]);
    }

    /**
     * @param Request $request
     * @return Closure
     */
    protected function transform(Request $request)
    {
        return fn ($notification) => NotificationResource::make($notification)->toArray($request);
    }

    /**
     * @param MarkNotificationWasReadRequest $request
     * @param MarkNotificationWasRead $processor
     * @return array
     */
    public function markNotificationRead(MarkNotificationWasReadRequest $request, MarkNotificationWasRead $processor)
    {
        $processor->execute($request->getNotification());

        return apiResponse()->success();
    }

    /**
     * @param Request $request
     * @param DeleteUser $processor
     * @return array
     */
    public function terminate(Request $request, DeleteUser $processor)
    {
        $processor->execute($request->user());

        return apiResponse()->success();
    }

    /**
     * @param Request $request
     * @param LogoutUser $processor
     * @return array
     */
    public function logout(Request $request, LogoutUser $processor)
    {
        $processor->execute($request);

        return apiResponse()->success();
    }

    /**
     * @param UpdatePhoneNumberRequest $request
     * @param UpdatePhoneNumber $processor
     * @return array
     */
    public function updatePhoneNumber(UpdatePhoneNumberRequest $request, UpdatePhoneNumber $processor)
    {
        $user = $processor->execute($request);

        return apiResponse()->success([
            'user' => UserResource::make($user)->toArray($request),
        ]);
    }

    /**
     * @param FindFriendRequest $request
     * @param FindFriend $processor
     * @return array
     */
    public function findFriend(FindFriendRequest $request, FindFriend $processor)
    {
        /** @var User[] $friends */
        $friends = $processor->execute($request->getPhones(), $request->user()->getKey());
        $friends = collect($friends)->sortBy('name');

        return apiResponse()->success([
            'friends' => array_values(UserResource::collection($friends)->toArray($request)),
        ]);
    }

    /**
     * @param Request $request
     * @param FindSchoolmate $processor
     * @return array
     */
    public function findSchoolmate(Request $request, FindSchoolmate $processor)
    {
        /** @var User[] $users */
        $users = $processor->execute((int) $request->get('school_id'), $request->user()->getKey());
        $users = collect($users)->sortBy('name');

        return apiResponse()->success([
            'users' => array_values(UserResource::collection($users)->toArray($request)),
        ]);
    }

    /**
     * @param RecommendationRequest $request
     * @param FetchRecommendation $processor
     * @return array
     * @throws \Http\Client\Exception
     * @throws \Typesense\Exceptions\TypesenseClientError
     */
    public function recommendation(RecommendationRequest $request, FetchRecommendation $processor)
    {
        $inCommunityId = (int) $request->get('community_id');
        if ($inCommunityId > 0) {
            /** @var Community $community */
            $community = communityRepository()->find($inCommunityId);

            /**
             * tạm thời không suggest khi post trong community không phải trường học
             */
            if (! $community->school_id) {
                return apiResponse()->success([
                    'users' => [],
                ]);
            }
        }

        $users = $processor->execute($request->user(), (string) $request->get('question'), $inCommunityId);

        return apiResponse()->success([
            'users' => array_values(UserResource::collection($users)->toArray($request)),
        ]);
    }

    /**
     * @param ImportRequest $request
     * @param UsersImport $processor
     * @return array
     */
    public function import(ImportRequest $request, UsersImport $processor)
    {
        $processor->import($request->file('file'), \Maatwebsite\Excel\Excel::XLSX);

        return apiResponse()->success();
    }

    /**
     * @param Request $request
     * @param ResetBadgeCount $processor
     * @return array
     */
    public function resetBadge(Request $request, ResetBadgeCount $processor)
    {
        $processor->execute($request->user());

        return apiResponse()->success();
    }

    /**
     * @param BlockUserRequest $request
     * @param BlockUser $processor
     * @return array
     */
    public function block(BlockUserRequest $request, BlockUser $processor)
    {
        $processor->execute($request->user(), $request->getBlockedUser()->getKey());

        return apiResponse()->success();
    }

    /**
     * @param UnblockUserRequest $request
     * @param UnblockUser $processor
     * @return array
     */
    public function unblock(UnblockUserRequest $request, UnblockUser $processor)
    {
        $processor->execute($request->user(), $request->getBlockedUser()->getKey());

        return apiResponse()->success();
    }

    /**
     * @param Request $request
     * @param ListBlockedUser $processor
     * @return array
     */
    public function blockedList(Request $request, ListBlockedUser $processor)
    {
        $users = $processor->execute($request->user());

        return apiResponse()->success([
            'users' => UserResource::collection($users)->toArray($request),
        ]);
    }

    /**
     * @param FirebaseAuthenticateRequest $request
     * @param CreateNewUser $creator
     * @return array
     * @throws BindingResolutionException
     */
    public function authenticateWithFirebase(FirebaseAuthenticateRequest $request, CreateNewUser $creator)
    {
        $firebaseUser = $request->getFirebaseUser();

        if (config('app.debug')) {
            chatWork()->report([
                'url' => $request->fullUrl(),
                'data' => [
                    'post' => $request->post(),
                ],
            ]);
        }

        $gender = Gender::tryFrom($request->post('gender'));

        $user = $request->getRegisteredUser() ?? $creator->create([
            'firebase_auth_id' => $firebaseUser->uid,
            'phone' => $firebaseUser->phoneNumber,
            'profile_photo_path' => $firebaseUser->photoUrl,
            'status' => 3, // verified phone number
            'referrer_id' => $request->getReferrerUser()?->getKey(),
            'total_coin' => 0,
            'total_point' => 0,
            'birthday' => $request->post('birthday'),
            'gender' => $gender->toInt(),
        ]);

        /**
         * mark the invited token as used
         */
        if ($inviteToken = $request->getInviteTokenInstance()) {
            $inviteToken->update([
                'status' => 1,
            ]);
        }

        /**
         * user đăng ký mới và có thông tin trường học
         */
        if ($user->wasRecentlyCreated) {
            $profile = $user->profile()->firstOrCreate();

            $age = $request->getAge();
            if ($age >= 18 && $age <= 24 && ($school = $request->getSchool())) {
                $profile->update([
                    'school_id' => $school->getKey(),
                ]);

                $user->updateQuietly([
                    'profile_type' => ProfileType::STUDENT->value,
                ]);
            }
        }

        auth()->login($user);

        return apiResponse()->success([
            'apiToken' => $user->createToken('')->plainTextToken,
            'user' => UserResource::make($user)->showProfile()->toArray($request),
        ]);
    }

    /**
     * @param GetRelationRequest $request
     * @param FetchUserRelation $fetcher
     * @return array
     * @throws BindingResolutionException
     */
    public function getFriends(GetRelationRequest $request, FetchUserRelation $fetcher)
    {
        return $this->getRelationData($request, $fetcher, 'friend');
    }

    /**
     * @param AddFriendRequest $request
     * @param AddFriend $processor
     * @return array
     */
    public function addFriend(AddFriendRequest $request, AddFriend $processor)
    {
        $processor->execute($request->user(), $request->getFriend());

        return apiResponse()->success();
    }

    /**
     * @param UnfriendRequest $request
     * @param Unfriend $processor
     * @return array
     */
    public function unfriend(UnfriendRequest $request, Unfriend $processor)
    {
        $processor->execute($request->user(), $request->getFriend());

        return apiResponse()->success();
    }

    /**
     * @param Request $request
     * @param ListPendingFriend $processor
     * @return array
     */
    public function pendingFriends(Request $request, ListPendingFriend $processor)
    {
        $users = $processor->execute($request->user());

        return apiResponse()->success([
            'users' => UserResource::collection($users)->toArray($request),
        ]);
    }

    /**
     * @param InviteFriendRequest $request
     * @param InviteFriend $processor
     * @return array
     */
    public function inviteFriend(InviteFriendRequest $request, InviteFriend $processor)
    {
        $processor->execute($request->user(), $request->getFriend());

        return apiResponse()->success();
    }

    /**
     * @param IgnoreFriendInvitationRequest $request
     * @param IgnoreFriendInvitation $processor
     * @return array
     */
    public function ignoreFriendInvitation(IgnoreFriendInvitationRequest $request, IgnoreFriendInvitation $processor)
    {
        $processor->execute($request->user(), $request->getFriend());

        return apiResponse()->success();
    }

    /**
     * @param CancelFriendInvitationRequest $request
     * @param CancelFriendInvitation $processor
     * @return array
     */
    public function cancelFriendInvitation(CancelFriendInvitationRequest $request, CancelFriendInvitation $processor)
    {
        $processor->execute($request->user(), $request->getFriend());

        return apiResponse()->success();
    }

    /**
     * @param Request $request
     * @return array
     */
    public function fetchFriendCount(Request $request)
    {
        /** @var User $user */
        $user = $request->user();

        $user->loadCount([
            'friends as friend_count' => function (Builder $query) {
                $query->whereHas('target', fn ($q) => $q->where('status', 1));
            },
        ]);

        return apiResponse()->success([
            'friend_count' => $user->getAttributeValue('friend_count'),
        ]);
    }

    /**
     * @param UpdateGenderRequest $request
     * @return array
     */
    public function updateGender(UpdateGenderRequest $request)
    {
        $user = $request->user();
        $reward = $user->emptyGender();

        $gender = Gender::tryFrom($request->post('gender'));

        $user->updateQuietly([
            'gender' => $gender->toInt(),
        ]);

        if ($reward) {
            GenderUpdated::dispatch($user);
        }

        return apiResponse()->success();
    }
}
