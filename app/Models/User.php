<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use App\Actions\Community\FetchListCommunity;
use App\Enums\Gender;
use App\Enums\ProfileType;
use App\Enums\UserRole;
use App\Http\Resources\Api\UserResource;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Http\Request;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\DB;
use Laravel\Fortify\TwoFactorAuthenticatable;
use Laravel\Jetstream\HasProfilePhoto;
use Laravel\Sanctum\HasApiTokens;
use <PERSON><PERSON>\Sanctum\NewAccessToken;
use Laravel\Sanctum\PersonalAccessToken;
use Laravel\Sanctum\TransientToken;

/**
 * @class User
 * @property int $user_id
 * @property string $uuid
 * @property string $firebase_auth_id
 * @property int $referrer_id
 * @property string $name
 * @property string $phone
 * @property string $email
 * @property string|Carbon $birthday
 * @property integer $gender
 * @property string $password
 * @property string $role
 * @property string $position
 * @property integer $total_coin
 * @property integer $used_coin
 * @property integer $holding_coin
 * @property integer $post_count
 * @property integer $answer_count
 * @property integer $best_answer_count
 * @property integer $comment_count
 * @property string $remember_token
 * @property string|Carbon $answer_started_at
 * @property string|Carbon $answer_ended_at
 * @property string|Carbon $email_verified_at
 * @property int $answer_status
 * @property int $status
 * @property Carbon $last_answered_post_at
 * @property Carbon $last_logged_in_at
 * @property string $profile_type
 * @property integer $badge_count
 * @property integer $news_badge_count
 * @property integer $invited_user_count
 * @property integer $max_invite_count
 * @property integer $referral_rewarded
 * @property integer $first_answer_rewarded
 * @property string $profile_photo_path
 * @property Carbon $created_at
 * @property Collection|PersonalAccessToken[] $tokens
 * @property Collection|Answer[] $answers
 * @property Collection|Post[] $posts
 * @property Collection|Device[] $devices
 * @property Collection|PostAnswer[] $postAnswers
 * @property Collection|UserRelation[] $followers
 * @property Collection|UserRelation[] $followings
 * @property Collection|UserRelation[] $friends
 * @property Collection|UserRelation[] $pendingFriends
 * @property Collection|UserRelation[] $invitationFriends
 * @property Collection|Notification[] $notifications
 * @property UserProfile|null $profile
 * @property User|null $referrer
 * @property Collection|UserRelation[] $relationship
 * @property Collection|CommunityUser[] $communityRelations
 * @property Collection|PremiumFeature[] $premiumFeatures
 */
class User extends Authenticatable
{
    use HasApiTokens;
    use HasFactory;
    use HasProfilePhoto;
    use Notifiable;
    use TwoFactorAuthenticatable;

    /**
     * @var string
     */
    protected $table = 'users';

    /**
     * @var string
     */
    protected $primaryKey = 'user_id';

    /**
     * @var array
     */
    protected $followingIds = [];

    /**
     * @var array
     */
    protected $followerIds = [];

    /**
     * @var array
     */
    protected $friendIds = [];

    /**
     * @var array
     */
    protected $pendingFriendIds = [];

    /**
     * @var array
     */
    protected $invitationIds = [];

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'uuid',
        'firebase_auth_id',
        'referrer_id',
        'name',
        'phone',
        'email',
        'password',
        'birthday',
        'gender',
        'role',
        'position',
        'total_coin',
        'used_coin',
        'holding_coin',
        'post_count',
        'answer_count',
        'best_answer_count',
        'comment_count',
        'answer_started_at',
        'answer_ended_at',
        'answer_status',
        'status',
        'last_answered_post_at',
        'last_logged_in_at',
        'profile_type',
        'profile_photo_path',
        'badge_count',
        'news_badge_count',
        'invited_user_count',
        'max_invite_count',
        'referral_rewarded',
        'first_answer_rewarded',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'uuid',
        'firebase_auth_id',
        'referrer_id',
        'birthday',
        'gender',
        'password',
        'remember_token',
        'two_factor_recovery_codes',
        'two_factor_secret',
        'email_verified_at',
        'role',
        'total_coin',
        'used_coin',
        'holding_coin',
        'current_team_id',
        'profile_photo_path',
        'created_at',
        'updated_at',
        'answer_started_at',
        'answer_ended_at',
        'answer_status',
        'status',
        'last_answered_post_at',
        'last_logged_in_at',
        'profile_type',
        'badge_count',
        'news_badge_count',
        'invited_user_count',
        'max_invite_count',
        'referral_rewarded',
        'first_answer_rewarded',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<int, string>
     */
    protected $appends = [
        'profile_photo_url',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'uuid' => 'string',
            'firebase_auth_id' => 'string',
            'referrer_id' => 'integer',
            'name' => 'string',
            'phone' => 'string',
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'birthday' => 'date',
            'gender' => 'integer',
            'role' => 'string',
            'position' => 'string',
            'total_coin' => 'integer',
            'used_coin' => 'integer',
            'post_count' => 'integer',
            'answer_count' => 'integer',
            'best_answer_count' => 'integer',
            'comment_count' => 'integer',
            'answer_started_at' => 'datetime',
            'answer_ended_at' => 'datetime',
            'answer_status' => 'integer',
            'status' => 'integer',
            'last_answered_post_at' => 'datetime',
            'last_logged_in_at' => 'datetime',
            'profile_type' => 'string',
            'badge_count' => 'integer',
            'invited_user_count' => 'integer',
            'max_invite_count' => 'integer',
            'referral_rewarded' => 'integer',
            'first_answer_rewarded' => 'integer',
        ];
    }

    /**
     * @return HasMany
     */
    public function answers(): HasMany
    {
        return $this->hasMany(Answer::class, 'user_id');
    }

    /**
     * @return HasMany
     */
    protected function fromRelationship()
    {
        return $this->hasMany(UserRelation::class, 'user_id');
    }

    /**
     * @return HasMany
     */
    protected function toRelationship()
    {
        return $this->hasMany(UserRelation::class, 'target_id');
    }

    /**
     * @return HasMany
     */
    public function followers()
    {
        return $this->toRelationship()
            ->where('is_follow', 1)
            ->where('is_blocked', 0);
    }

    /**
     * @return HasMany
     */
    public function pendingFriends()
    {
        return $this->toRelationship()
            ->where('is_friend', 2)
            ->where('is_blocked', 0);
    }

    /**
     * @return HasMany
     */
    public function invitationFriends()
    {
        return $this->fromRelationship()
            ->where('is_friend', 2)
            ->where('is_blocked', 0);
    }

    /**
     * @return HasMany
     */
    public function friends()
    {
        return $this->fromRelationship()
            ->where('is_friend', 1)
            ->where('is_blocked', 0);
    }

    /**
     * @return HasMany
     */
    public function followings()
    {
        return $this->fromRelationship()
            ->where('is_follow', 1)
            ->where('is_blocked', 0);
    }

    /**
     * @return HasMany
     */
    public function posts(): HasMany
    {
        return $this->hasMany(Post::class, 'user_id');
    }

    /**
     * @return HasMany
     */
    public function postAnswers(): HasMany
    {
        return $this->hasMany(PostAnswer::class, 'user_id');
    }

    /**
     * @return HasMany
     */
    public function devices(): HasMany
    {
        return $this->hasMany(Device::class, 'user_id');
    }

    /**
     * @return HasMany
     */
    public function notifications(): HasMany
    {
        return $this->hasMany(Notification::class, 'user_id');
    }

    /**
     * @return HasMany
     */
    public function reactions()
    {
        return $this->hasMany(Reaction::class, 'user_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function profile()
    {
        return $this->hasOne(UserProfile::class, 'user_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo<User, User>
     */
    public function referrer()
    {
        return $this->belongsTo(User::class, 'referrer_id');
    }

    /**
     * @return HasMany
     */
    public function communityRelations()
    {
        return $this->hasMany(CommunityUser::class, 'user_id');
    }

    /**
     * @return HasMany
     */
    public function relationship()
    {
        return $this->fromRelationship();
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function premiumFeatures()
    {
        return $this->belongsToMany(PremiumFeature::class, 'user_premium_features', 'user_id', 'premium_id');
    }

    /**
     * @return bool
     */
    public function isAdmin(): bool
    {
        return $this->role === UserRole::ADMINISTRATOR->value;
    }

    /**
     * @return bool
     */
    public function isSuperAdmin()
    {
        return $this->isAdmin() && (1 === (int) $this->getKey());
    }

    /**
     * @return bool
     */
    public function isEnabled()
    {
        return 1 === (int) $this->status;
    }

    /**
     * @return bool
     */
    public function isPending()
    {
        return 2 === (int) $this->status;
    }

    /**
     * @return bool
     */
    public function isVerified()
    {
        return 3 === (int) $this->status;
    }

    /**
     * @return bool
     */
    public function isDisabled()
    {
        return 0 === (int) $this->status;
    }

    /**
     * @return array
     */
    public function getAnswerTime()
    {
        $now = now('UTC')->toDateTimeString();
        $payload = [
            'answer_ended_at' => $now,
        ];

        if (! $this->answer_started_at) {
            $payload['answer_started_at'] = $now;
        }

        return $payload;
    }

    /**
     * @return void
     */
    public function markSurveyAnswerStatusAsCompleted()
    {
        $this->update([
            'answer_status' => 1,
        ]);
    }

    /**
     * @param array $ids
     * @return void
     */
    protected function setFollowings(array $ids)
    {
        $this->followingIds = $ids;
    }

    /**
     * @param array $ids
     * @return void
     */
    protected function setFollowers(array $ids)
    {
        $this->followerIds = $ids;
    }

    /**
     * @param array $ids
     * @return void
     */
    protected function setFriends(array $ids)
    {
        $this->friendIds = $ids;
    }

    /**
     * @param array $ids
     * @return void
     */
    protected function setPendingFriends(array $ids)
    {
        $this->pendingFriendIds = $ids;
    }

    /**
     * @param array $ids
     * @return void
     */
    protected function setInvitationFriends(array $ids)
    {
        $this->invitationIds = $ids;
    }

    /**
     * @param int $id
     * @return bool
     */
    public function isFollowing(int $id)
    {
        return in_array($id, $this->followingIds);
    }

    /**
     * @param int $id
     * @return bool
     */
    public function isFollower(int $id)
    {
        return in_array($id, $this->followerIds);
    }

    /**
     * @param int $id
     * @return bool
     */
    public function isFriend(int $id)
    {
        return in_array($id, $this->friendIds);
    }

    /**
     * @param int $id
     * @return bool
     */
    public function isPendingFriend(int $id)
    {
        return in_array($id, $this->pendingFriendIds);
    }

    /**
     * @param int $id
     * @return bool
     */
    public function isFriendInvitationSent(int $id)
    {
        return in_array($id, $this->invitationIds);
    }

    /**
     * @return void
     */
    public function prepareRelationshipData()
    {
        $this->load([
            /*'followings',
            'followers',*/
            'friends',
            'pendingFriends',
            'invitationFriends',
        ]);

        //$this->setFollowers($this->followers->pluck('user_id')->toArray());
        //$this->setFollowings($this->followings->pluck('target_id')->toArray());

        $this->setFriends($this->friends->pluck('target_id')->toArray());
        $this->setPendingFriends($this->pendingFriends->pluck('user_id')->toArray());
        $this->setInvitationFriends($this->invitationFriends->pluck('target_id')->toArray());
    }

    /**
     * @return void
     */
    public function updateLatestLoggedInTime()
    {
        $this->updateQuietly([
            'last_logged_in_at' => now('UTC')->toDateTimeString(),
        ]);
    }

    /**
     * @param string $column
     * @param string $type
     * @param int $amount
     * @param array $extra
     * @return void
     */
    public function incrementOrDecrementCount(string $column = 'post_count', string $type = 'increment', int $amount = 1, array $extra = [])
    {
        $this->incrementOrDecrement($column, $amount, $extra, $type);
    }

    /**
     * @return string[]
     */
    public function getDeviceTokens()
    {
        $this->loadMissing([
            'devices' => function (HasMany $query) {
                $query->where('status', 1);
            },
        ]);

        return $this->devices->pluck('token')->toArray();
    }

    /**
     * @return string
     */
    public function currentTokenId()
    {
        /** @var PersonalAccessToken|TransientToken $currentAccessToken */
        $currentAccessToken = $this->currentAccessToken();
        if ($currentAccessToken instanceof TransientToken) {
            return null;
        }

        return (string) ($currentAccessToken?->getKey() ?? request()->session()->getId() . '_' . $this->getKey());
    }

    /**
     * @return string
     */
    public function getPhoneNumber()
    {
        if ($this->isEnabled()) {
            return $this->phone;
        }

        $phone = explode('_', $this->phone);

        return $phone[0];
    }

    /**
     * @return int|string
     */
    public function getAge()
    {
        if (! $this->birthday) {
            return '';
        }

        return datetime()->getAgeFromDate($this->birthday);
    }

    /**
     * @return void
     */
    public function loadProfile()
    {
        /**
         * load profile
         */
        $this->load([
            'profile',
            'premiumFeatures',
        ]);

        if ($this->profile_type === ProfileType::STUDENT->value) {
            $this->load('profile.school');
        }
    }

    /**
     * @param int $objectId
     * @param string $objectType
     * @return bool
     */
    public function isMuted(int $objectId, string $objectType = 'post')
    {
        return DB::table('reactions')->where([
            'object_id' => $objectId,
            'object_type' => $objectType,
            'user_id' => $this->getKey(),
            'action' => 'muted',
        ])->exists();
    }

    /**
     * @param array $ids
     * @return bool
     */
    public function isMutedAny(array $ids)
    {
        return DB::table('reactions')
            ->whereIn('object_id', $ids)
            ->whereIn('object_type', ['post', 'post_answer'])
            ->where([
                'user_id' => $this->getKey(),
                'action' => 'muted',
            ])
            ->exists();
    }

    /**
     * @param int $objectId
     * @param string $objectType
     * @return bool
     */
    public function isNotMuted(int $objectId, string $objectType = 'post')
    {
        return ! $this->isMuted($objectId, $objectType);
    }

    /**
     * @param array $ids
     * @return bool
     */
    public function isNotMutedAny(array $ids)
    {
        return ! $this->isMutedAny($ids);
    }

    /**
     * @return void
     */
    public function rebuildProfileData()
    {
        $this->profile?->touch();
    }

    /**
     * @return bool
     */
    public function canInviteUser()
    {
        return $this->invited_user_count < $this->max_invite_count;
    }

    /**
     * @return int
     */
    public function totalUsedCoin()
    {
        return (int) ($this->used_coin + $this->holding_coin);
    }

    /**
     * @return int
     */
    public function getRemainingCoins()
    {
        return (int) ($this->total_coin - $this->totalUsedCoin());
    }

    /**
     * @param int $coin
     * @return bool
     */
    public function hasEnoughCoins(int $coin)
    {
        return $coin <= $this->getRemainingCoins();
    }

    /**
     * @param int $id
     * @return bool
     */
    public function isID($id)
    {
        $id = (int) $id;

        return $id === (int) $this->getKey();
    }

    /**
     * @param $id
     * @return bool
     */
    public function isNotID($id)
    {
        return ! $this->isID($id);
    }

    /**
     * Determines if a user has been blocked.
     *
     * @param int $userId
     * @return bool
     */
    public function wasBlocked(int $userId)
    {
        $blockedIds = blockHelper()->getBlockedIds($this->getKey());

        return in_array($userId, $blockedIds);
    }

    /**
     * @param PostAnswer $answer
     * @return bool
     */
    public function canDeleteAnswer(PostAnswer $answer)
    {
        $userId = $this->getKey();

        return $answer->isEnable()
            && $answer->isNotVoted()
            && (
                $answer->ownerBy($userId) || $answer->post->ownerBy($userId)
            );
    }

    /**
     * @param $communityId
     * @return bool
     */
    public function isMemberOf($communityId)
    {
        if (! $this->relationLoaded('communityRelations')) {
            $this->loadMissing([
                'communityRelations' => function (HasMany $query) use ($communityId) {
                    $query->where('community_id', $communityId);
                },
            ]);
        }

        if ($communityRelation = $this->communityRelations->first(fn ($c) => $c->isCommunity($communityId))) {
            return 1 === (int) $communityRelation->status;
        }

        return false;
    }

    /**
     * @param $communityId
     * @return bool
     */
    public function isNotMemberOf($communityId)
    {
        return ! $this->isMemberOf($communityId);
    }

    /**
     * @param Request $request
     * @return array
     */
    public function toResource(Request $request)
    {
        return UserResource::make($this)->toArray($request);
    }

    /**
     * @return Collection
     */
    public function getJoinedCommunities(bool $isClosed = false)
    {
        /** @var FetchListCommunity $fetchCommunity */
        $fetchCommunity = app(FetchListCommunity::class);

        return $fetchCommunity->execute($this, $isClosed);
    }

    /**
     * @return bool
     */
    public function isMale()
    {
        return 1 == $this->gender;
    }

    /**
     * @return bool
     */
    public function isFemale()
    {
        return 2 == $this->gender;
    }

    /**
     * @return bool
     */
    public function isOtherGender()
    {
        return 3 == $this->gender;
    }

    /**
     * @return bool
     */
    public function emptyGender()
    {
        return ! $this->gender;
    }

    /**
     * @return string
     */
    public function genderString()
    {
        return match ($this->gender) {
            1 => Gender::MALE->value,
            2 => Gender::FEMALE->value,
            3 => Gender::OTHER->value,
            default => '',
        };
    }
}
