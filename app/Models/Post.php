<?php

namespace App\Models;

use App\Models\Contracts\Reportable;
use App\Models\Traits\HasStatus;
use App\Models\Traits\ReportableObject;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Casts\AsArrayObject;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Query\JoinClause;

/**
 * @class Post
 * @property integer $post_id
 * @property integer $user_id
 * @property string $content
 * @property string $image
 * @property string $type
 * @property integer $like_count
 * @property integer $answer_count
 * @property integer $report_count
 * @property string|array $extra
 * @property string|Carbon $first_answered_at
 * @property string|Carbon $latest_answered_at
 * @property string|Carbon $sort_answered_at
 * @property int $voted
 * @property int $reward_amount
 * @property int $featured
 * @property integer $status
 * @property Carbon $created_at
 * @property User $createdBy
 * @property Collection|PostAnswer[] $answers
 * @property Collection|Reaction[] $reactions
 * @property Media[]|Collection $media
 * @property CommunityPost $communityPost
 * @property MetaData|null $metadata
 */
class Post extends Model implements Reportable
{
    use HasFactory;
    use HasStatus;
    use ReportableObject;

    /**
     * @var string
     */
    public $table = 'posts';

    /**
     * @var string
     */
    public $primaryKey = 'post_id';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'content',
        'image',
        'type',
        'like_count',
        'answer_count',
        'report_count',
        'options',
        'extra',
        'first_answered_at',
        'latest_answered_at',
        'sort_answered_at',
        'voted',
        'reward_amount',
        'featured',
        'status',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'first_answered_at',
        'latest_answered_at',
        'sort_answered_at',
        'created_at',
        'updated_at',
    ];

    /**
     * @inheritdoc
     */
    protected function casts(): array
    {
        return [
            'user_id' => 'integer',
            'content' => 'string',
            'image' => 'string',
            'type' => 'string',
            'like_count' => 'integer',
            'answer_count' => 'integer',
            'report_count' => 'integer',
            'options' => AsArrayObject::class,
            'extra' => AsArrayObject::class,
            'first_answered_at' => 'datetime',
            'latest_answered_at' => 'datetime',
            'sort_answered_at' => 'datetime',
            'voted' => 'integer',
            'reward_amount' => 'integer',
            'featured' => 'integer',
            'status' => 'integer',
        ];
    }

    /**
     * @return BelongsTo
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * @return HasMany
     */
    public function answers(): HasMany
    {
        return $this->hasMany(PostAnswer::class, 'post_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function relationshipAnswered()
    {
        return $this->hasOne(PostAnswer::class, 'post_id')
            ->join('user_relations', function(JoinClause $join) {
                $join->on('post_answers.user_id', '=', 'user_relations.target_id')
                    ->where('user_relations.is_friend', 1);
            });
    }

    /**
     * @return MorphMany
     */
    public function reactions(): MorphMany
    {
        return $this->morphMany(Reaction::class, 'object');
    }

    /**
     * @return HasMany
     */
    public function media()
    {
        return $this->hasMany(Media::class, 'post_id');
    }

    /**
     * @return BelongsTo
     */
    public function communityPost()
    {
        return $this->belongsTo(CommunityPost::class, 'post_id', 'post_id');
    }

    /**
     * @param string $column
     * @param int $amount
     * @param bool $updateFirstTime
     * @return void
     */
    public function incrementCount(string $column = 'answer_count', int $amount = 1, bool $updateFirstTime = false)
    {
        $extra = [];
        if ($column === 'answer_count') {
            $now = now('UTC')->toDateTimeString();
            $extra['latest_answered_at'] = $now;

            if ($updateFirstTime && ! $this->first_answered_at) {
                $extra['first_answered_at'] = $now;
            }
        }

        $this->incrementQuietly($column, $amount, $extra);
    }

    /**
     * @param string $column
     * @param int $amount
     * @return void
     */
    public function decrementCount(string $column = 'answer_count', int $amount = 1)
    {
        $this->decrementQuietly($column, $amount);
    }

    /**
     * @return bool
     */
    public function ownerBy($userId)
    {
        $userId = (int) $userId;

        return $userId === (int) $this->user_id;
    }

    /**
     * @param $userId
     * @return bool
     */
    public function isNotOwnerBy($userId)
    {
        return ! $this->ownerBy($userId);
    }

    /**
     * Determine if the entity was voted on.
     *
     * @return bool
     */
    public function wasVoted()
    {
        return 1 === (int) $this->voted;
    }

    /**
     * @return int
     */
    public function getRewardAmount()
    {
        return (int) $this->reward_amount;
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\MorphOne
     */
    public function metadata()
    {
        return $this->morphOne(MetaData::class, 'metadata');
    }
}
