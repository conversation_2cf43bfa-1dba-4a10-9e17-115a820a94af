<?php

namespace App\Repositories;

use App\Models\Post;
use App\Models\Traits\IgnoreViewedPost;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

/**
 * Class PostRepository
 * @package App\Repositories
 */
class PostRepository extends BaseRepository
{
    use IgnoreViewedPost;

    /**
     * @return string
     */
    protected function getModelClass(): string
    {
        return Post::class;
    }

    /**
     * @param Builder $query
     * @param array $payload
     * @param string|null $asTable
     * @return void
     */
    protected function applyQueryFilters(Builder $query, array $payload, ?string $asTable = null): void
    {
        $table = $this->getTable();
        if (! $asTable) {
            $asTable = $table;
        }

        if ($asTable !== $table) {
            $query->getModel()->setTable($asTable);
        }

        /**
         * filter by user_id
         */
        $userId = Arr::get($payload, 'userId');
        $query->when($userId, fn ($q) => $q->where('user_id', (int) $userId));

        /**
         * filter by status
         */
        $query->when(! is_null($status = Arr::get($payload, 'status')), fn ($q) => $q->where('status', (int) $status));

        /**
         * posts in community
         */
        $communityId = Arr::get($payload, 'communityId');
        $query->when(! is_null($communityId), function (Builder $query) use ($communityId) {
            $query->whereHas('communityPost', function (Builder $query) use ($communityId) {
                $query->where('community_id', $communityId);

                $query->whereExists(
                    DB::table('community_users')
                        ->whereColumn('community_posts.community_id', 'community_users.community_id')
                        ->where('community_users.status', 1)
                );
            });
        });

        /**
         * topic status
         */
        $topics = Arr::get($payload, 'topics');
        $query->when(in_array($topics, ['yes', 'no']), fn ($q) => $q->where('featured', (int) ($topics === 'yes')));

        /**
         * reward posts
         */
        if (Arr::get($payload, 'hasReward')) {
            $query->where('voted', 0)
                ->where('reward_amount', '>', 0);
        }

        /**
         * filter by user enabled
         */
        $query->when(! $userId, function (Builder $q) use ($payload, $asTable) {
            /**
             * check blocked users
             */
            if (Arr::get($payload, 'checkBlocked')) {
                $q->when(blockHelper()->getBlockedIds(auth()->id()), fn ($q, $blockedIds) => $q->whereNotIn('user_id', $blockedIds));
            }

            $q->whereHas('createdBy', function (Builder $q) use ($payload) {
                $q->when(! Arr::get($payload, 'all'), fn ($q) => $q->select('user_id')->where('status', 1));

                /**
                 * kiểm tra bài post của những người cùng giới tính
                 */
                if (Arr::get($payload, 'checkGender')) {
                    $this->ignoreGenderPosts($q);
                }

                $q->when(Arr::get($payload, 'searchUser'), fn ($q, $searchUser) => $q->where('name', 'LIKE', '%' .$searchUser. '%'));
            });

            /**
             * kiểm tra bài post trong community mở
             * hoặc trong những community đóng mà mình tham gia
             */
            if (Arr::get($payload, 'checkCommunity')) {
                $this->ignoreCloseCommunityPosts($q, '`' .$asTable. '`.`post_id`');
            }
        });

        /**
         * report status
         */
        $reported = Arr::get($payload, 'reported');
        $query->when(in_array($reported, ['yes', 'no']), fn ($q) => $q->where('report_count', $reported === 'yes' ? '>' : '=', 0));

        /**
         * search content
         */
        $query->when(Arr::get($payload, 'search'), function ($q, $search) {
            is_numeric($search) ? $q->where('post_id', (int) $search) : $q->where('content', 'LIKE', '%' .$search. '%');
        });

        $query->getModel()->setTable($table);
    }

    /**
     * @param int $limit
     * @param int $page
     * @return \Illuminate\Pagination\LengthAwarePaginator
     * @throws BindingResolutionException
     */
    public function getRewardPosts(int $limit = 100, int $page = 1)
    {
        $offset = ($page - 1) * $limit;

        $payload = [
            'orderBy' => 'post_id',
            'orderDirection' => 'desc',
            'all' => false,
            'fetchTotal' => false,
            'status' => 1,
            'hasReward' => true,
            'checkBlocked' => true,
            'checkCommunity' => true,
            'checkGender' => true,
            'with' => [
                'media',
            ],
        ];

        return $this->pagination($payload, $limit, $page, $offset);
    }
}
