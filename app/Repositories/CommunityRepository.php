<?php

namespace App\Repositories;

use App\Models\Community;
use Illuminate\Database\Eloquent\Builder;

/**
 * Class CommunityRepository
 * @package App\Repositories
 */
class CommunityRepository extends BaseRepository
{
    /**
     * @return string
     */
    protected function getModelClass(): string
    {
        return Community::class;
    }

    /**
     * @param Builder $query
     * @param array $payload
     * @param string|null $asTable
     * @return void
     */
    protected function applyQueryFilters(Builder $query, array $payload, ?string $asTable = null): void
    {

    }
}
